// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 初始化日期选择器
    initDatePickers();
    
    // 初始化表单提交事件
    initFormSubmit();
    
    // 初始化表格排序功能
    initTableSort();
    
    // 初始化浮动面板
    initFloatingPanel();
    
    // 初始化快速搜索
    initQuickSearch();
    
    // 初始化多列下拉菜单
    initMultiColumnDropdown();
    
    // 初始化多功能按钮
    initMultiFunctionButton();
    
    // 默认激活可视化标签
    const floatingPanel = document.getElementById('floating-search-panel');
    if (floatingPanel) {
        const visualizationContent = document.getElementById('visualization-tab');
        
        if (visualizationContent) {
            // 隐藏所有内容区域
            document.querySelectorAll('.panel-tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 激活可视化内容
            visualizationContent.classList.add('active');
            
            // 加载可视化数据
            loadTabData('visualization-tab');
        }
    }
});

/**
 * 初始化多列下拉菜单
 */
function initMultiColumnDropdown() {
    const menuItems = document.querySelectorAll('.menu-item');
    const navItems = document.querySelectorAll('.nav-item.dropdown');
    
    // 添加点击导航项目显示/隐藏下拉菜单功能
    navItems.forEach(item => {
        const navLink = item.querySelector('.nav-link');
        const dropdownMenu = item.querySelector('.dropdown-menu');
        
        if (navLink && dropdownMenu) {
            // 初始隐藏下拉菜单
                if (window.innerWidth <= 768) {
                        dropdownMenu.style.display = 'none';
            }
            
            // 点击事件 - 在移动设备上切换显示/隐藏
            navLink.addEventListener('click', function(e) {
                e.preventDefault();
                
                // 无论是桌面还是移动设备，都确保下拉菜单可见
                if (window.innerWidth <= 768) {
                    // 设置初始样式以确保平滑过渡
                    dropdownMenu.style.display = 'block';
                    // 使用 requestAnimationFrame 确保 DOM 更新后再添加过渡类
                    requestAnimationFrame(() => {
                        requestAnimationFrame(() => {
                            dropdownMenu.classList.add('show');
                        });
                    });
                }
            });
            
            // 添加关闭按钮事件
            dropdownMenu.addEventListener('click', function(e) {
                // 如果点击了关闭按钮的位置（右上角）
                if (window.innerWidth <= 768 && 
                    ((e.target === this && e.clientX > (this.offsetWidth - 50) && e.clientY < 50) ||
                     e.target.closest('.dropdown-menu::after'))) {
                    this.classList.remove('show');
                    // 监听过渡完成事件
                    this.addEventListener('transitionend', function handler() {
                        this.style.display = 'none';
                        this.removeEventListener('transitionend', handler);
                    }, { once: true });
                }
            });
            
            // 点击其他区域时隐藏菜单
            document.addEventListener('click', function(e) {
                if (!item.contains(e.target)) {
                    if (window.innerWidth <= 768 && dropdownMenu.classList.contains('show')) {
                        dropdownMenu.classList.remove('show');
                        // 监听过渡完成事件
                        dropdownMenu.addEventListener('transitionend', function handler() {
                            this.style.display = 'none';
                            this.removeEventListener('transitionend', handler);
                        }, { once: true });
                    }
                }
            });
        }
    });
    
    // 监听窗口大小变化
    window.addEventListener('resize', function() {
        navItems.forEach(item => {
            const dropdownMenu = item.querySelector('.dropdown-menu');
            if (dropdownMenu) {
                    if (window.innerWidth <= 768) {
                    if (!dropdownMenu.classList.contains('show')) {
                        dropdownMenu.style.display = 'none';
                    }
                } else {
                    dropdownMenu.style.display = '';
                    dropdownMenu.classList.remove('show');
                }
            }
        });
    });
    
    // 添加移动端滑动支持（针对下拉菜单）
    let touchStartX = 0;
    let touchEndX = 0;
    let touchStartY = 0;
    let touchCurrentX = 0;
    let activeMenu = null;
    let touchMenuStarted = false;
    
    // 监听触摸开始事件
    document.addEventListener('touchstart', function(e) {
        touchStartX = e.changedTouches[0].screenX;
        touchStartY = e.changedTouches[0].screenY;
        touchCurrentX = touchStartX;
        
        // 检查是否是下拉菜单
        activeMenu = document.querySelector('.dropdown-menu.show');
        touchMenuStarted = activeMenu !== null;
    }, { passive: true });
    
    // 监听触摸移动事件，实现实时跟随
    document.addEventListener('touchmove', function(e) {
        if (!touchMenuStarted || !activeMenu) return;
        
        touchCurrentX = e.changedTouches[0].screenX;
        const deltaX = touchCurrentX - touchStartX;
        
        // 向左滑动才响应，且最多到原始位置
        if (deltaX < 0) {
            const newPosition = Math.max(deltaX, -activeMenu.offsetWidth);
            // 使用 transform 替代 left 属性以获得更好的性能
            activeMenu.style.transform = `translateX(${newPosition}px)`;
        }
    }, { passive: true });
    
    // 监听触摸结束事件
    document.addEventListener('touchend', function(e) {
        touchEndX = e.changedTouches[0].screenX;
        const deltaX = touchEndX - touchStartX;
        const deltaY = e.changedTouches[0].screenY - touchStartY;
        
        // 确保是水平滑动而不是垂直滑动
        if (Math.abs(deltaX) > Math.abs(deltaY)) {
            handleMenuSwipe(deltaX);
        }
        
        // 重置实时跟随
        if (activeMenu) {
            activeMenu.style.transform = '';
        }
        
        touchMenuStarted = false;
        activeMenu = null;
    }, { passive: true });
    
    // 处理滑动手势
    function handleMenuSwipe(deltaX) {
        // 向左滑动（隐藏下拉菜单）
        if (deltaX < -50) {
            const visibleMenu = document.querySelector('.dropdown-menu.show');
            if (visibleMenu) {
                visibleMenu.classList.remove('show');
                visibleMenu.addEventListener('transitionend', function handler() {
                    this.style.display = 'none';
                    this.removeEventListener('transitionend', handler);
                }, { once: true });
            }
        }
    }
    
    menuItems.forEach(item => {
        item.addEventListener('click', function(e) {
            // 如果是链接到浮动面板的选项，则显示浮动面板
            if (this.getAttribute('href') === '#') {
                e.preventDefault();
                const panelId = this.getAttribute('data-panel');
                if (panelId) {
                    const floatingPanel = document.getElementById('floating-search-panel');
                    
                    if (floatingPanel) {
                        // 显示浮动面板
                        floatingPanel.classList.add('active');
                        
                        // 显示对应的内容
                        document.querySelectorAll('.panel-tab-content').forEach(content => {
                            content.classList.remove('active');
                        });
                        document.getElementById(panelId).classList.add('active');
                        
                        // 加载数据
                        loadTabData(panelId);
                        
                        // 关闭下拉菜单
                        const visibleMenu = document.querySelector('.dropdown-menu.show');
                        if (visibleMenu && window.innerWidth <= 768) {
                            visibleMenu.classList.remove('show');
                            visibleMenu.addEventListener('transitionend', function handler() {
                                this.style.display = 'none';
                                document.body.style.overflow = '';
                                this.removeEventListener('transitionend', handler);
                            }, { once: true });
                        }
                    }
                }
            }
        });
    });
}

/**
 * 快速搜索功能已删除
 */
function initQuickSearch() {
    // 空函数，保留接口兼容性
    console.log('Quick search functionality has been deprecated');
}

/**
 * 初始化浮动面板
 */
function initFloatingPanel() {
    const floatingPanel = document.getElementById('floating-search-panel');
    const closePanel = document.querySelector('.close-panel');
    const menuItems = document.querySelectorAll('.menu-item[data-panel]');
    
    if (!floatingPanel) return;
    
    // 关闭面板按钮点击事件
    if (closePanel) {
        closePanel.addEventListener('click', function() {
            floatingPanel.classList.remove('active');
            document.body.style.overflow = ''; // 恢复背景滚动
        });
    }
    
    // 点击面板外部关闭面板
    document.addEventListener('click', function(e) {
        // 如果点击的不是面板内的元素，且面板是可见的，则关闭面板
        if (floatingPanel.classList.contains('active') && 
            !floatingPanel.contains(e.target) && 
            !e.target.closest('#function-icon') &&
            !e.target.closest('#show-panel-btn')) {
            floatingPanel.classList.remove('active');
            document.body.style.overflow = ''; // 恢复背景滚动
        }
    });
    
    // 菜单项点击事件
    menuItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            
            // 获取面板ID
            const panelId = this.getAttribute('data-panel');
            if (!panelId) return;
                
                // 隐藏所有内容区域
                document.querySelectorAll('.panel-tab-content').forEach(content => {
                    content.classList.remove('active');
                });
                
            // 激活选中的内容区域
            const targetContent = document.getElementById(panelId);
            if (targetContent) {
                targetContent.classList.add('active');
                
                // 加载对应的数据
                loadTabData(panelId);
            }
            
            // 显示面板
            floatingPanel.classList.add('active');
            });
        });
    
    // 添加滑动支持
    let touchStartX = 0;
    let touchEndX = 0;
    let touchStartY = 0;
    let touchCurrentX = 0;
    let touchPanelStarted = false;
    
    // 监听触摸开始事件
    floatingPanel.addEventListener('touchstart', function(e) {
        touchStartX = e.changedTouches[0].screenX;
        touchStartY = e.changedTouches[0].screenY;
        touchCurrentX = touchStartX;
        touchPanelStarted = true;
    }, { passive: true });
    
    // 监听触摸移动事件，实现实时跟随
    floatingPanel.addEventListener('touchmove', function(e) {
        if (!touchPanelStarted) return;
        
        touchCurrentX = e.changedTouches[0].screenX;
        const deltaX = touchCurrentX - touchStartX;
        
        // 向左滑动才响应，且最多到原始位置
        if (deltaX < 0) {
            const newPosition = Math.max(deltaX, -floatingPanel.offsetWidth);
            // 使用 transform 替代 left 属性以获得更好的性能
            floatingPanel.style.transform = `translateX(${newPosition}px)`;
        }
    }, { passive: true });
    
    // 监听触摸结束事件
    floatingPanel.addEventListener('touchend', function(e) {
        touchEndX = e.changedTouches[0].screenX;
        const deltaX = touchEndX - touchStartX;
        const deltaY = e.changedTouches[0].screenY - touchStartY;
        
        // 确保是水平滑动而不是垂直滑动
        if (Math.abs(deltaX) > Math.abs(deltaY)) {
            handlePanelSwipe(deltaX);
        }
        
        // 重置实时跟随
        floatingPanel.style.transform = '';
        touchPanelStarted = false;
    }, { passive: true });
    
    // 处理滑动手势
    function handlePanelSwipe(deltaX) {
        // 向左滑动（隐藏面板）
        if (deltaX < -50) {
            floatingPanel.classList.remove('active');
            document.body.style.overflow = ''; // 恢复背景滚动
        }
    }
    
    // 初始加载面板数据
    loadPanelData();
}

/**
 * 加载面板数据
 */
function loadPanelData() {
    // 获取当前活动的标签
    const activeTab = document.querySelector('.panel-tab.active');
    if (activeTab) {
        const tabId = activeTab.getAttribute('data-tab');
        loadTabData(tabId);
    }
}

/**
 * 加载特定标签的数据
 */
function loadTabData(tabId) {
    switch(tabId) {
        case 'visualization-tab':
            loadVisualizationData();
            break;
        case 'data-analysis-tab':
            loadAnalyticsData();
            break;
        case 'inspection-records-tab':
            loadInspectionRecords();
            break;
        case 'new-inspection-tab':
            // 表单页面，不需要加载数据
            break;

    }
}

/**
 * 加载检验记录数据
 */
function loadInspectionRecords(page = 1, type = 'all') {
    const recordsList = document.getElementById('inspection-records-list');
    const pageInfo = document.getElementById('records-page-info');
    const prevButton = document.getElementById('prev-records-page');
    const nextButton = document.getElementById('next-records-page');
    const typeFilter = document.getElementById('records-type-filter');
    
    if (!recordsList) return;
    
    // 显示加载中
    recordsList.innerHTML = '<div class="loading-indicator">加载中...</div>';
    
    // 构建查询参数
    const params = new URLSearchParams();
    params.append('page', page);
    params.append('per_page', 10);
    if (type !== 'all') {
        params.append('type', type);
    }
    
    // 发送请求获取检验记录
    fetch(`/incoming/api/inspection_records?${params.toString()}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 更新页码信息
                if (pageInfo) {
                    pageInfo.textContent = `第 ${data.page} 页，共 ${data.total_pages} 页`;
                }
                
                // 更新分页按钮状态
                if (prevButton) {
                    prevButton.disabled = data.page <= 1;
                }
                if (nextButton) {
                    nextButton.disabled = data.page >= data.total_pages;
                }
                
                // 更新类型过滤器
                if (typeFilter && !typeFilter.eventAdded) {
                    typeFilter.value = type;
                    typeFilter.addEventListener('change', function() {
                        loadInspectionRecords(1, this.value);
                    });
                    typeFilter.eventAdded = true;
                }
                
                // 绑定分页按钮事件
                if (prevButton && !prevButton.eventAdded) {
                    prevButton.addEventListener('click', function() {
                        if (data.page > 1) {
                            loadInspectionRecords(data.page - 1, typeFilter ? typeFilter.value : 'all');
                        }
                    });
                    prevButton.eventAdded = true;
                }
                
                if (nextButton && !nextButton.eventAdded) {
                    nextButton.addEventListener('click', function() {
                        if (data.page < data.total_pages) {
                            loadInspectionRecords(data.page + 1, typeFilter ? typeFilter.value : 'all');
                        }
                    });
                    nextButton.eventAdded = true;
                }
                
                // 显示记录
                if (data.records && data.records.length > 0) {
                    let html = '<table class="table-sm"><thead><tr><th>物料号</th><th>名称</th><th>供应商</th><th>日期</th><th>合格率</th><th></th></tr></thead><tbody>';
                    
                    data.records.forEach(record => {
                        // 由于已删除full_inspection，所有记录都是sampling类型
                        const inspectionType = 'sampling';
                        let qualifiedRate;

                        // 计算合格率（抽样检验）
                        const qualifiedQuantity = record.sample_quantity - (record.defect_quantity || 0);
                        qualifiedRate = (qualifiedQuantity / record.sample_quantity) * 100;

                        html += `
                            <tr>
                                <td>${record.material_number}</td>
                                <td>${record.material_name}</td>
                                <td>${record.supplier}</td>
                                <td>${new Date(record.inspection_date).toLocaleDateString()}</td>
                                <td>${qualifiedRate.toFixed(2)}%</td>
                                <td>
                                    <button class="btn btn-sm btn-primary" onclick="viewInspectionDetails('${record.id}', '${inspectionType}')">详情</button>
                                </td>
                            </tr>
                        `;
                    });
                    
                    html += '</tbody></table>';
                    recordsList.innerHTML = html;
                } else {
                    recordsList.innerHTML = '<div class="no-data">暂无检验记录</div>';
                }
            } else {
                recordsList.innerHTML = `<div class="error-message">加载失败: ${data.error}</div>`;
            }
        })
        .catch(error => {
            recordsList.innerHTML = `<div class="error-message">加载失败: ${error}</div>`;
            console.error('加载检验记录失败:', error);
        });
}

/**
 * 加载可视化数据
 */
function loadVisualizationData() {
    // 加载检验合格率趋势图
    fetch('/incoming/api/inspection_statistics')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 这里可以使用图表库如Chart.js来渲染图表
                document.getElementById('trend-chart').innerHTML = '数据已加载，可以使用图表库渲染';
                document.getElementById('supplier-chart').innerHTML = '数据已加载，可以使用图表库渲染';
            }
        })
        .catch(error => {
            console.error('加载可视化数据失败:', error);
        });
}

/**
 * 加载数据分析数据
 */
function loadAnalyticsData() {
    fetch('/incoming/api/inspection_statistics')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 这里可以使用图表库如Chart.js来渲染图表
                document.getElementById('rate-trend-chart').innerHTML = '数据已加载，可以使用图表库渲染';
                document.getElementById('supplier-dist-chart').innerHTML = '数据已加载，可以使用图表库渲染';
                document.getElementById('issue-chart').innerHTML = '数据已加载，可以使用图表库渲染';
                
                // 加载最近检验记录
                loadRecentInspections();
            }
        })
        .catch(error => {
            console.error('加载数据分析失败:', error);
        });
}

/**
 * 加载最近检验记录
 */
function loadRecentInspections() {
    fetch('/incoming/api/recent_inspections')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const container = document.getElementById('recent-inspections-list');
                if (!container) return;
                
                if (!data.data || data.data.length === 0) {
                    container.innerHTML = '<p>暂无检验记录</p>';
                    return;
                }
                
                let html = '<table class="table-sm"><thead><tr><th>物料</th><th>供应商</th><th>日期</th><th>合格率</th></tr></thead><tbody>';
                
                data.data.forEach(record => {
                    const qualifiedRate = (record.qualified_quantity / record.total_quantity * 100).toFixed(2);
                    html += `
                        <tr>
                            <td>${record.material_number}</td>
                            <td>${record.supplier}</td>
                            <td>${new Date(record.inspection_date).toLocaleDateString()}</td>
                            <td>${qualifiedRate}%</td>
                        </tr>
                    `;
                });
                
                html += '</tbody></table>';
                container.innerHTML = html;
            }
        })
        .catch(error => {
            console.error('加载最近检验记录失败:', error);
        });
}



/**
 * 在面板中显示搜索结果
 */
function displayPanelSearchResults(data) {
    const resultsContainer = document.getElementById('search-results');
    if (!resultsContainer) return;
    
    if (!data.results || data.results.length === 0) {
        resultsContainer.innerHTML = '<p>没有找到匹配的记录</p>';
        return;
    }
    
    let html = `<p>找到 ${data.count} 条记录</p>`;
    html += '<table class="table-sm"><thead><tr><th>物料号</th><th>名称</th><th>供应商</th><th>日期</th><th>合格率</th><th></th></tr></thead><tbody>';
    
    data.results.forEach(record => {
        // 由于已删除full_inspection，所有记录都是sampling类型
        const inspectionType = 'sampling';
        let qualifiedRate;

        // 计算合格率（抽样检验）
        const qualifiedQuantity = record.sample_quantity - (record.defect_quantity || 0);
        qualifiedRate = (qualifiedQuantity / record.sample_quantity) * 100;

        html += `
            <tr>
                <td>${record.material_number}</td>
                <td>${record.material_name}</td>
                <td>${record.supplier}</td>
                <td>${new Date(record.inspection_date).toLocaleDateString()}</td>
                <td>${qualifiedRate.toFixed(2)}%</td>
                <td>
                    <button class="btn btn-sm btn-primary" onclick="viewInspectionDetails('${record.id}', '${inspectionType}')">详情</button>
                </td>
            </tr>
        `;
    });
    
    html += '</tbody></table>';
    resultsContainer.innerHTML = html;
}

/**
 * 显示搜索结果
 */
function displaySearchResults(data) {
    const tbody = document.querySelector('.sortable-table tbody');
    if (!tbody) return;
    
    tbody.innerHTML = '';
    
    if (!data.records || data.records.length === 0) {
        const tr = document.createElement('tr');
        const colCount = document.querySelectorAll('.sortable-table thead th').length || 14;
        tr.innerHTML = `<td colspan="${colCount}" style="text-align: center;">没有找到匹配的记录</td>`;
        tbody.appendChild(tr);
        return;
    }
    
    // 确定当前页面类型
    const currentPath = window.location.pathname;
    const isSampling = true; // 只支持抽样检验
    
    data.records.forEach((record, index) => {
        const tr = document.createElement('tr');
        let qualifiedRate;
        
        if (isSampling) {
            qualifiedRate = record.sample_quantity / record.total_quantity * 100;
        } else {
            qualifiedRate = record.qualified_quantity / record.total_quantity * 100;
        }
        
        // 构建行内容
        const quantityCol = isSampling ? 
            `<td data-column="sample_quantity">${record.sample_quantity}</td>` : 
            `<td data-column="qualified_quantity">${record.qualified_quantity}</td>`;
            
        // 截断问题点文本，如果超过20个字符则显示省略号
        const defectIssues = record.defect_issues ? 
            (record.defect_issues.length > 20 ? record.defect_issues.substring(0, 20) + '...' : record.defect_issues) : 
            '-';
            
        tr.innerHTML = `
            <td data-column="id">${(data.page - 1) * data.per_page + index + 1}</td>
            <td data-column="material_number">${record.material_number}</td>
            <td data-column="material_name">${record.material_name}</td>
            <td data-column="specification">${record.specification || ''}</td>
            <td data-column="supplier">${record.supplier}</td>
            <td data-column="purchase_order">${record.purchase_order}</td>
            <td data-column="receipt_date">${new Date(record.receipt_date).toLocaleDateString()}</td>
            <td data-column="inspection_date">${new Date(record.inspection_date).toLocaleDateString()}</td>
            <td data-column="total_quantity">${record.total_quantity}</td>
            ${quantityCol}
            <td data-column="defect_quantity">${record.defect_quantity}</td>
            <td data-column="defect_issues">${defectIssues}</td>
            <td data-column="qualified_rate">${qualifiedRate.toFixed(2)}%</td>
            <td>
                <button class="btn btn-primary" onclick="viewInspectionDetails('${record.id}', 'sampling')">详情</button>
            </td>
        `;
        
        tbody.appendChild(tr);
    });
    
    // 更新统计信息
    const summary = document.querySelector('.summary');
    if (summary) {
        summary.textContent = `显示 ${data.records.length} 条记录，共 ${data.total_records} 条记录`;
    }
    
    // 更新分页
    updatePagination(data);
}

/**
 * 更新分页控件
 */
function updatePagination(data) {
    const paginationDiv = document.querySelector('.pagination');
    if (!paginationDiv) return;
    
    if (data.total_pages <= 1) {
        paginationDiv.style.display = 'none';
        return;
    }
    
    paginationDiv.style.display = 'flex';
    paginationDiv.innerHTML = '';
    
    // 首页和上一页
    if (data.page > 1) {
        addPaginationLink(paginationDiv, 1, '&laquo; 首页', data);
        addPaginationLink(paginationDiv, data.page - 1, '&lsaquo; 上一页', data);
    } else {
        addPaginationSpan(paginationDiv, '&laquo; 首页', 'disabled');
        addPaginationSpan(paginationDiv, '&lsaquo; 上一页', 'disabled');
    }
    
    // 页码
    for (let i = Math.max(1, data.page - 2); i <= Math.min(data.total_pages, data.page + 2); i++) {
        if (i === data.page) {
            addPaginationSpan(paginationDiv, i.toString(), 'active');
        } else {
            addPaginationLink(paginationDiv, i, i.toString(), data);
        }
    }
    
    // 下一页和末页
    if (data.page < data.total_pages) {
        addPaginationLink(paginationDiv, data.page + 1, '下一页 &rsaquo;', data);
        addPaginationLink(paginationDiv, data.total_pages, '末页 &raquo;', data);
    } else {
        addPaginationSpan(paginationDiv, '下一页 &rsaquo;', 'disabled');
        addPaginationSpan(paginationDiv, '末页 &raquo;', 'disabled');
    }
}

/**
 * 添加分页链接
 */
function addPaginationLink(container, page, text, data) {
    const a = document.createElement('a');
    a.href = '#';
    a.innerHTML = text;
    a.addEventListener('click', function(e) {
        e.preventDefault();
        
        // 确定当前页面类型
        const currentPath = window.location.pathname;
        let inspectionType = 'sampling'; // 只支持抽样检验
        
        // 构建URL参数
        const params = new URLSearchParams();
        params.append('page', page);
        params.append('per_page', data.per_page);
        
        // 发送请求
        fetch(`/incoming/api/search/${inspectionType}?${params.toString()}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displaySearchResults(data);
                }
            });
    });
    container.appendChild(a);
}

/**
 * 添加分页文本
 */
function addPaginationSpan(container, text, className) {
    const span = document.createElement('span');
    span.innerHTML = text;
    span.className = className;
    container.appendChild(span);
}

/**
 * 初始化日期选择器
 */
function initDatePickers() {
    const dateInputs = document.querySelectorAll('input[type="date"]');
    const today = new Date().toISOString().split('T')[0];
    dateInputs.forEach(input => {
        if (!input.value) {
            input.value = today;
        }
    });
}

/**
 * 初始化表单提交
 */
function initFormSubmit() {
    const inspectionForm = document.getElementById('inspection-form');
    if (inspectionForm) {
        inspectionForm.addEventListener('submit', function(event) {
            event.preventDefault();
            
            // 收集表单数据
            const formData = new FormData();

            // 基本信息
            formData.append('material_number', document.getElementById('material-number')?.value || '');
            formData.append('material_name', document.getElementById('material-name')?.value || '');
            formData.append('specification', document.getElementById('specification')?.value || '');
            formData.append('material_type', document.getElementById('material-type')?.value || '');
            formData.append('color', document.getElementById('color')?.value || '');
            formData.append('supplier', document.getElementById('supplier')?.value || '');
            formData.append('purchase_order', document.getElementById('purchase-order')?.value || '');
            formData.append('receipt_date', document.getElementById('receipt-date')?.value || '');
            formData.append('inspection_date', document.getElementById('inspection-date')?.value || '');
            formData.append('total_quantity', document.getElementById('total-quantity')?.value || '0');
            formData.append('defect_quantity', document.getElementById('defect-quantity')?.value || '0');

            // 检查是否有sample-quantity字段（抽样检验）或qualified-quantity字段（全检）
            const sampleQuantityEl = document.getElementById('sample-quantity');
            const qualifiedQuantityEl = document.getElementById('qualified-quantity');

            if (sampleQuantityEl) {
                formData.append('sample_quantity', sampleQuantityEl.value || '0');
            } else if (qualifiedQuantityEl) {
                formData.append('qualified_quantity', qualifiedQuantityEl.value || '0');
            }

            formData.append('defect_issues', document.getElementById('defect-issues')?.value || '');

            // 检验类型
            const inspectionTypeEl = document.querySelector('input[name="inspection-type"]:checked') ||
                                   document.querySelector('input[name="inspection_type"]:checked');
            formData.append('inspection_type', inspectionTypeEl?.value || 'sampling');

            // 发送数据到后端
            fetch('/incoming/api/add_inspection', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('检验记录添加成功！');
                    window.location.href = '/sampling_inspection';
                } else {
                    alert('添加失败: ' + data.error);
                }
            })
            .catch(error => {
                alert('添加失败: ' + error);
                console.error('提交错误:', error);
            });
        });
    }
}

/**
 * 初始化表格排序
 */
function initTableSort() {
    const tables = document.querySelectorAll('.sortable-table');
    tables.forEach(table => {
        const headers = table.querySelectorAll('th[data-sort]');
        headers.forEach(header => {
            header.addEventListener('click', function() {
                const column = this.dataset.sort;
                const direction = this.classList.contains('asc') ? 'desc' : 'asc';
                
                headers.forEach(h => h.classList.remove('asc', 'desc'));
                this.classList.add(direction);
                
                sortTable(table, column, direction);
            });
        });
    });
}

/**
 * 排序表格
 */
function sortTable(table, column, direction) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    
    // 比较函数
    const compare = (a, b) => {
        const cellA = a.querySelector(`td[data-column="${column}"]`).textContent.trim();
        const cellB = b.querySelector(`td[data-column="${column}"]`).textContent.trim();
        
        // 检查是否为日期
        if (column.includes('date')) {
            return direction === 'asc' 
                ? new Date(cellA) - new Date(cellB)
                : new Date(cellB) - new Date(cellA);
        }
        
        // 检查是否为数字
        if (!isNaN(cellA) && !isNaN(cellB)) {
            return direction === 'asc' 
                ? parseFloat(cellA) - parseFloat(cellB)
                : parseFloat(cellB) - parseFloat(cellA);
        }
        
        // 字符串比较
        return direction === 'asc' ? cellA.localeCompare(cellB) : cellB.localeCompare(cellA);
    };
    
    // 排序行并重新添加
    tbody.append(...rows.sort(compare));
}

/**
 * 查看检验记录详情
 */
function viewInspectionDetails(recordId, inspectionType = 'sampling') {
    // 如果inspectionType未定义或为空，默认为sampling
    if (!inspectionType || inspectionType === 'undefined') {
        inspectionType = 'sampling';
    }

    console.log(`查看检验详情: recordId=${recordId}, inspectionType=${inspectionType}`);

    // 检查是否有模态框
    const existingModal = document.getElementById('record-modal');
    
    if (existingModal) {
        // 使用页面中已存在的模态框
        fetch(`/incoming/api/inspection_details/${inspectionType}/${recordId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 填充详情模态框
                    const record = data.record;
                    const detailsDiv = document.querySelector('.record-details');
                    
                    // 构建详情HTML
                    let detailsHTML = `
                        <p><strong>物料料号:</strong> ${record.material_number}</p>
                        <p><strong>物料名称:</strong> ${record.material_name}</p>
                        <p><strong>规格:</strong> ${record.specification || ''}</p>
                        <p><strong>材质:</strong> ${record.material_type || ''}</p>
                        <p><strong>颜色:</strong> ${record.color || ''}</p>
                        <p><strong>供应商:</strong> ${record.supplier}</p>
                        <p><strong>采购单号:</strong> ${record.purchase_order}</p>
                        <p><strong>来料日期:</strong> ${new Date(record.receipt_date).toLocaleDateString()}</p>
                        <p><strong>检验日期:</strong> ${new Date(record.inspection_date).toLocaleDateString()}</p>
                        <p><strong>来料数量:</strong> ${record.total_quantity}</p>
                    `;
                    
                    if (inspectionType === 'sampling') {
                        detailsHTML += `
                            <p><strong>抽样数量:</strong> ${record.sample_quantity}</p>
                            <p><strong>不良数量:</strong> ${record.defect_quantity}</p>
                            <p><strong>抽样比例:</strong> ${(record.sample_quantity / record.total_quantity * 100).toFixed(2)}%</p>
                        `;
                    } else {
                        detailsHTML += `
                            <p><strong>合格数量:</strong> ${record.qualified_quantity}</p>
                            <p><strong>不良数量:</strong> ${record.defect_quantity}</p>
                            <p><strong>合格率:</strong> ${(record.qualified_quantity / record.total_quantity * 100).toFixed(2)}%</p>
                        `;
                    }
                    
                    detailsHTML += `<p><strong>不良问题:</strong> ${record.defect_issues || '无'}</p>`;
                    
                    detailsDiv.innerHTML = detailsHTML;
                    
                    // 显示模态框
                    existingModal.style.display = 'block';
                } else {
                    alert('获取记录详情失败: ' + data.error);
                }
            })
            .catch(error => {
                alert('获取记录详情失败: ' + error);
                console.error('获取记录详情错误:', error);
            });
    } else {
        // 创建新的模态框
        fetch(`/api/inspection_details/${inspectionType}/${recordId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayRecordDetails(data.record);
            } else {
                alert('获取详情失败: ' + data.error);
            }
        })
        .catch(error => {
            alert('获取详情失败: ' + error);
            console.error('获取详情错误:', error);
        });
    }
}

/**
 * 显示记录详情
 */
function displayRecordDetails(record) {
    // 创建模态框
    const modal = document.createElement('div');
    modal.className = 'modal';
    
    // 确定显示的文本，抽样检验显示"抽样数量"，全部检验显示"合格数量"
    const isSampling = record.inspection_type === 'sampling';
    const quantityLabel = isSampling ? '抽样数量:' : '合格数量:';
    const quantityValue = isSampling ? record.sample_quantity : record.qualified_quantity;
    
    modal.innerHTML = `
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2 style="font-size: 16px; margin: 0 0 10px 0;">检验记录详情</h2>
            <div class="record-details">
                <p><strong>物料料号:</strong> ${record.material_number}</p>
                <p><strong>物料名称:</strong> ${record.material_name}</p>
                <p><strong>规格:</strong> ${record.specification}</p>
                <p><strong>材质:</strong> ${record.material_type || '未指定'}</p>
                <p><strong>颜色:</strong> ${record.color || '未指定'}</p>
                <p><strong>供应商:</strong> ${record.supplier}</p>
                <p><strong>采购单号:</strong> ${record.purchase_order}</p>
                <p><strong>来料日期:</strong> ${new Date(record.receipt_date).toLocaleDateString()}</p>
                <p><strong>检验日期:</strong> ${new Date(record.inspection_date).toLocaleDateString()}</p>
                <p><strong>来料数量:</strong> ${record.total_quantity}</p>
                <p><strong>${quantityLabel}</strong> ${quantityValue}</p>
                <p><strong>不良数量:</strong> ${record.defect_quantity}</p>
                <p><strong>合格率:</strong> ${(quantityValue / record.total_quantity * 100).toFixed(2)}%</p>
                <p><strong>不良问题:</strong> ${record.defect_issues}</p>
                <p><strong>检验类型:</strong> ${record.inspection_type === 'sampling' ? '抽样检验' : '全部检验'}</p>
            </div>
        </div>
    `;
    
    // 添加模态框到页面并显示
    document.body.appendChild(modal);
    modal.style.display = 'block';
    
    // 关闭模态框的点击事件
    modal.querySelector('.close').addEventListener('click', () => {
        modal.style.display = 'none';
        document.body.removeChild(modal);
    });
    
    // 点击模态框外部区域关闭
    window.addEventListener('click', event => {
        if (event.target === modal) {
            modal.style.display = 'none';
            document.body.removeChild(modal);
        }
    });
}

/**
 * 初始化多功能按钮
 */
function initMultiFunctionButton() {
    const functionIcon = document.getElementById('function-icon');
    const functionDropdown = document.querySelector('.function-dropdown');
    const showPanelBtn = document.getElementById('show-panel-btn');
    const settingsBtn = document.getElementById('settings-btn');
    const floatingPanel = document.getElementById('floating-search-panel');
    const multiFunctionBtn = document.querySelector('.multi-function-btn');
    
    // 标记是否正在进行鼠标移动到菜单的过程中
    let isMouseMovingToDropdown = false;
    
    // 点击多功能按钮显示/隐藏下拉菜单
    if (functionIcon && functionDropdown) {
        functionIcon.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            // 切换下拉菜单的显示状态
            if (functionDropdown.style.display === 'block') {
                functionDropdown.style.display = 'none';
            } else {
                // 计算下拉菜单位置
                const iconRect = functionIcon.getBoundingClientRect();
                functionDropdown.style.top = (iconRect.bottom + 5) + 'px';
                functionDropdown.style.right = (window.innerWidth - iconRect.right) + 'px';
                functionDropdown.style.display = 'block';
            }
        });
        
        // 鼠标悬停显示下拉菜单（桌面设备）
        if (window.innerWidth > 768) {
            if (multiFunctionBtn) {
                multiFunctionBtn.addEventListener('mouseenter', function() {
                    // 计算下拉菜单位置
                    const iconRect = functionIcon.getBoundingClientRect();
                    functionDropdown.style.top = (iconRect.bottom + 5) + 'px';
                    functionDropdown.style.right = (window.innerWidth - iconRect.right) + 'px';
                    functionDropdown.style.display = 'block';
                    // 设置标记，表示正在从按钮移向下拉菜单
                    isMouseMovingToDropdown = true;
                    
                    // 100毫秒后重置标记，给用户足够时间移动到下拉菜单
                    setTimeout(() => {
                        isMouseMovingToDropdown = false;
                    }, 300);
                });
                
                multiFunctionBtn.addEventListener('mouseleave', function(e) {
                    // 检查鼠标是否移动到下拉菜单方向
                    // 计算移动方向
                    const dropdownRect = functionDropdown.getBoundingClientRect();
                    const mouseX = e.clientX;
                    const mouseY = e.clientY;
                    
                    // 只有当鼠标不是朝着下拉菜单方向移动时，才考虑隐藏菜单
                    // 这里使用简单的检测：如果鼠标位置接近下拉菜单区域，认为是向菜单移动
                    if (Math.abs(mouseX - dropdownRect.left) > 100 || 
                        Math.abs(mouseY - dropdownRect.top) > 50) {
                        // 鼠标不是朝着下拉菜单移动，可以考虑隐藏
                        // 但不立即隐藏，给一个短暂的延迟
                        setTimeout(() => {
                            // 如果鼠标不在下拉菜单内，且不是正在移动到菜单的过程中，才隐藏
                            const overDropdown = document.querySelector('.function-dropdown:hover') !== null;
                            if (!overDropdown && !isMouseMovingToDropdown) {
                                functionDropdown.style.display = 'none';
                            }
                        }, 50);
                    }
                });
            }
            
            // 鼠标进入下拉菜单
            functionDropdown.addEventListener('mouseenter', function() {
                // 进入下拉菜单后取消隐藏
                isMouseMovingToDropdown = false;
                this.style.display = 'block';
            });
            
            // 鼠标离开下拉菜单时隐藏
            functionDropdown.addEventListener('mouseleave', function() {
                functionDropdown.style.display = 'none';
            });
        }
        
        // 点击页面其他区域关闭下拉菜单
        document.addEventListener('click', function(e) {
            if (functionDropdown.style.display === 'block' && 
                !functionDropdown.contains(e.target) && 
                e.target !== functionIcon) {
                functionDropdown.style.display = 'none';
            }
        });
    }
    
    // 显示数据面板按钮点击事件
    if (showPanelBtn && floatingPanel) {
        showPanelBtn.addEventListener('click', function(e) {
            e.preventDefault();
            
            // 显示浮动面板
            floatingPanel.classList.add('active');
            document.body.style.overflow = 'hidden'; // 防止背景滚动
            
            // 关闭下拉菜单
            if (functionDropdown) {
                functionDropdown.style.display = 'none';
            }
        });
    }
    
    // 系统设置按钮点击事件
    if (settingsBtn) {
        settingsBtn.addEventListener('click', function(e) {
            // 不阻止默认行为，让链接正常跳转到系统设置页面

            // 关闭下拉菜单
            if (functionDropdown) {
                functionDropdown.style.display = 'none';
            }
        });
    }
    
    // 监听窗口大小变化，重新计算下拉菜单位置
    window.addEventListener('resize', function() {
        if (functionDropdown && functionDropdown.style.display === 'block' && functionIcon) {
            const iconRect = functionIcon.getBoundingClientRect();
            functionDropdown.style.top = (iconRect.bottom + 5) + 'px';
            functionDropdown.style.right = (window.innerWidth - iconRect.right) + 'px';
        }
    });
} 